<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">Purchase Tokens</h1>

        <div class="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
            <div v-if="user" class="mb-6">
                <p class="text-strava-grayMedium mb-2">Current token balance:</p>
                <p class="text-3xl font-bold text-strava-gray">{{ user.tokens }} <span class="text-xl">tokens</span></p>
            </div>

            <div v-if="message" class="p-4 mb-6" :class="messageClass">
                <p>{{ message }}</p>
            </div>

            <h2 class="text-xl font-bold text-strava-gray mb-6">Select a package:</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div 
                    v-for="(pkg, index) in packages" 
                    :key="index" 
                    class="border rounded-lg p-4 text-center hover:shadow-lg transition cursor-pointer"
                    :class="{ 'border-strava-orange': selectedPackage === index, 'border-gray-200': selectedPackage !== index }"
                    @click="selectPackage(index)"
                >
                    <h3 class="text-lg font-bold text-strava-gray mb-2">{{ pkg.name }}</h3>
                    <p class="text-2xl font-bold text-strava-orange mb-1">${{ pkg.price }}</p>
                    <p class="text-strava-grayLight mb-4">{{ pkg.tokens }} tokens</p>
                    <p class="text-xs text-strava-grayLight">{{ pkg.description }}</p>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-bold text-strava-gray mb-2">Payment Method</h3>
                <div class="border border-gray-200 rounded-lg p-4">
                    <p class="text-strava-grayLight mb-4">Secure payment via Stripe</p>
                    
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-strava-grayLight mb-2" for="card-name">Name on Card</label>
                            <input type="text" id="card-name" v-model="cardName" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                        </div>
                        <div>
                            <label class="block text-strava-grayLight mb-2" for="card-number">Card Number</label>
                            <input type="text" id="card-number" v-model="cardNumber" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" placeholder="4242 4242 4242 4242">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="block text-strava-grayLight mb-2" for="card-expiry-month">Expiry Month</label>
                            <input type="text" id="card-expiry-month" v-model="expiryMonth" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" placeholder="MM">
                        </div>
                        <div>
                            <label class="block text-strava-grayLight mb-2" for="card-expiry-year">Expiry Year</label>
                            <input type="text" id="card-expiry-year" v-model="expiryYear" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" placeholder="YYYY">
                        </div>
                        <div>
                            <label class="block text-strava-grayLight mb-2" for="card-cvc">CVC</label>
                            <input type="text" id="card-cvc" v-model="cardCVC" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" placeholder="123">
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end">
                <button 
                    @click="processPayment" 
                    class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    :disabled="loading || !selectedPackage"
                >
                    {{ loading ? 'Processing...' : 'Purchase Tokens' }}
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';

export default {
    name: 'PurchaseTokens',
    setup() {
        const router = useRouter();
        const user = ref(null);
        const selectedPackage = ref(null);
        const message = ref('');
        const messageClass = ref('');
        const loading = ref(false);
        
        // Card details
        const cardName = ref('');
        const cardNumber = ref('');
        const expiryMonth = ref('');
        const expiryYear = ref('');
        const cardCVC = ref('');
        
        const packages = [
            {
                name: 'Basic',
                tokens: 5,
                price: '4.99',
                description: 'Perfect for occasional exports'
            },
            {
                name: 'Standard',
                tokens: 15,
                price: '9.99',
                description: 'Most popular option'
            },
            {
                name: 'Premium',
                tokens: 30,
                price: '14.99',
                description: 'Best value for active users'
            }
        ];
        
        onMounted(async () => {
            await fetchUserData();
        });
        
        const fetchUserData = async () => {
            try {
                const response = await axios.get('/api/user');
                user.value = response.data;
            } catch (error) {
                console.error('Error fetching user data:', error);
                // Redirect to login if not authenticated
                if (error.response && error.response.status === 401) {
                    router.push({ name: 'login' });
                }
            }
        };
        
        const selectPackage = (index) => {
            selectedPackage.value = index;
        };
        
        const processPayment = async () => {
            if (selectedPackage.value === null) return;
            
            loading.value = true;
            message.value = '';
            
            // Simple validation
            if (!cardName.value || !cardNumber.value || !expiryMonth.value || !expiryYear.value || !cardCVC.value) {
                message.value = 'Please fill in all card details';
                messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
                loading.value = false;
                return;
            }
            
            try {
                // In a real app, this would integrate with Stripe or another payment processor
                // For this demo, we'll simulate a successful payment
                
                // Add tokens to user account
                const pkg = packages[selectedPackage.value];
                const response = await axios.post('/api/tokens', {
                    tokens: pkg.tokens
                });
                
                // Update user data
                user.value = response.data.user || user.value;
                user.value.tokens = response.data.tokens;
                
                // Show success message
                message.value = `Successfully purchased ${pkg.tokens} tokens!`;
                messageClass.value = 'bg-green-100 text-green-700 border-l-4 border-green-500';
                
                // Reset form
                cardName.value = '';
                cardNumber.value = '';
                expiryMonth.value = '';
                expiryYear.value = '';
                cardCVC.value = '';
                selectedPackage.value = null;
                
            } catch (error) {
                console.error('Payment error:', error);
                message.value = 'Payment failed. Please try again.';
                messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
            } finally {
                loading.value = false;
            }
        };
        
        return {
            user,
            packages,
            selectedPackage,
            message,
            messageClass,
            loading,
            cardName,
            cardNumber,
            expiryMonth,
            expiryYear,
            cardCVC,
            selectPackage,
            processPayment
        };
    }
}
</script>

<style>
/* Strava style customizations */
input:focus {
    outline: none;
    border-color: #FC4C02;
}
</style>
