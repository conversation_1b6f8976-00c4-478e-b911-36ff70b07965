<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">Generate Activity</h1>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="md:col-span-2">
                <div class="bg-white p-4 rounded-lg shadow-md mb-6">
                    <h2 class="text-xl font-bold text-strava-gray mb-4">Route Creator</h2>

                    <!-- Add search box above the map -->
                    <div class="mb-2 relative">
                        <div class="flex">
                            <input
                                ref="searchInput"
                                type="text"
                                v-model="searchQuery"
                                @input="onSearchInput"
                                @keydown="handleKeydown"
                                @focus="showSuggestions = true"
                                @blur="onSearchBlur"
                                placeholder="Search for a location..."
                                class="w-full border rounded-l px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange"
                                autocomplete="off"
                            >
                            <button
                                @click="searchLocation"
                                class="bg-strava-orange text-white px-4 py-2 rounded-r hover:bg-strava-orangeLight"
                            >
                                Search
                            </button>
                        </div>

                        <!-- Autocomplete dropdown -->
                        <div
                            v-if="showSuggestions && suggestions.length > 0"
                            class="absolute z-[9999] w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto"
                            style="z-index: 9999;"
                        >
                            <div
                                v-for="(suggestion, index) in suggestions"
                                :key="suggestion.id"
                                @mousedown="selectSuggestion(suggestion)"
                                @mouseenter="highlightedIndex = index"
                                class="px-3 py-2 cursor-pointer hover:bg-gray-100 border-b border-gray-100 last:border-b-0"
                                :class="{ 'bg-strava-orange bg-opacity-10': highlightedIndex === index }"
                            >
                                <div class="font-medium text-gray-900">{{ suggestion.text }}</div>
                                <div class="text-sm text-gray-600">{{ suggestion.place_name }}</div>
                            </div>
                        </div>

                        <!-- Loading indicator -->
                        <div
                            v-if="isSearching"
                            class="absolute right-12 top-1/2 transform -translate-y-1/2"
                        >
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-strava-orange"></div>
                        </div>

                        <p v-if="searchError" class="text-red-500 text-sm mt-1">{{ searchError }}</p>
                    </div>
                    <div id="map" class="map-container rounded mb-4"></div>

                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <p class="text-strava-grayLight">{{ distance }} km | {{ elevationGain }} m elevation gain</p>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="setDrawingMode('point')" class="bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded text-sm" :class="{ 'bg-strava-orange text-white hover:bg-strava-orangeLight': drawingMode === 'point' }">Add Points</button>
                            <button
                                @click="alignToRoad"
                                :disabled="routePoints.length < 2 || isAligning"
                                class="px-3 py-1 rounded text-sm transition-colors"
                                :class="routePoints.length < 2 || isAligning ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'"
                                :title="getAlignmentTooltip()"
                            >
                                {{ isAligning ? 'Aligning...' : getAlignmentButtonText() }}
                            </button>
                            <button @click="clearRoute" class="bg-strava-grayMedium text-white px-3 py-1 rounded text-sm hover:bg-strava-gray">Clear</button>
                        </div>
                    </div>

                    <!-- Error message for road alignment -->
                    <div v-if="alignmentError" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        {{ alignmentError }}
                    </div>

                    <!-- Helpful tips for route alignment -->
                    <div v-if="routePoints.length >= 2 && !isAligning && !alignmentError" class="mb-4 p-3 bg-blue-50 border border-blue-200 text-blue-700 rounded text-sm">
                        <div class="flex items-start">
                            <svg class="w-4 h-4 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <strong>Route Alignment Tips:</strong>
                                <ul class="mt-1 list-disc list-inside space-y-1">
                                    <li>Place points on or near roads/paths suitable for {{ routeType }}</li>
                                    <li>Use fewer points (max 25) for better routing success</li>
                                    <li>Avoid placing points in water, buildings, or restricted areas</li>
                                    <li v-if="routeType === 'bike'">For cycling: Points should be near bike-accessible roads</li>
                                    <li v-else>For walking/running: Points should be near pedestrian paths</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-4 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold text-strava-gray mb-4">Activity Preview</h2>

                    <div v-if="routePoints.length > 0 && activityGenerated" class="space-y-4">
                        <div class="border rounded p-4">
                            <h3 class="font-semibold mb-2 text-strava-grayMedium">Activity Summary</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div>
                                    <p class="text-sm text-strava-grayLight">Distance</p>
                                    <p class="font-bold text-strava-gray">{{ distance }} km</p>
                                </div>
                                <div>
                                    <p class="text-sm text-strava-grayLight">Duration</p>
                                    <p class="font-bold text-strava-gray">{{ formatDuration(calculatedDuration) }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-strava-grayLight">Avg Pace</p>
                                    <p class="font-bold text-strava-gray">{{ formatPaceDisplay(pace) }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-strava-grayLight">Avg Heart Rate</p>
                                    <p class="font-bold text-strava-gray">{{ avgHeartRate }} bpm</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button @click="goToExport" class="bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">Continue to Export</button>
                        </div>
                    </div>

                    <div v-else class="text-center py-8">
                        <p class="text-strava-grayLight mb-4">Configure your activity parameters and click Generate</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-md h-fit">
                <h2 class="text-xl font-bold text-strava-gray mb-4">Activity Parameters</h2>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="activity-name">Activity Name</label>
                    <input type="text" id="activity-name" v-model="activityName" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="route-type">Activity Type</label>
                    <select id="route-type" v-model="routeType" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                        <option value="running">Run</option>
                        <option value="bike">Bike</option>
                        <option value="hike">Hike</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="activity-date">Activity Date</label>
                    <input type="date" id="activity-date" v-model="activityDate" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="activity-time">Start Time</label>
                    <input type="time" id="activity-time" v-model="activityTime" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="pace">Pace (min/km)</label>
                    <input type="number" id="pace" v-model="pace" step="0.1" min="3" max="15" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                    <input type="range" v-model="pace" min="3" max="15" step="0.1" class="w-full mt-2 accent-strava-orange">
                    <p class="text-sm text-strava-grayLight mt-1">
                        {{ formatPaceDisplay(pace) }} (This is how it will appear in Strava)
                    </p>
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="pace-variability">Pace Variability (%)</label>
                    <input type="number" id="pace-variability" v-model="paceVariability" step="1" min="0" max="100" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="avg-heart-rate">Avg Heart Rate (bpm)</label>
                    <input type="number" id="avg-heart-rate" v-model="avgHeartRate" step="1" min="60" max="200" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="cadence">Cadence (spm)</label>
                    <input type="number" id="cadence" v-model="cadence" step="1" min="150" max="200" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="flex justify-end">
                    <button @click="generateActivity" class="bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">Generate</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { onMounted, onUnmounted, ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

export default {
    name: 'ActivityGenerator',
    setup() {
        const router = useRouter();
        const map = ref(null);
        const routeLayer = ref(null);
        const routePoints = ref([]);
        const distance = ref(0);
        const elevationGain = ref(0);
        const drawingMode = ref('point');
        const searchQuery = ref('');
        const searchError = ref('');
        const isAligning = ref(false);
        const alignmentError = ref('');

        // Autocomplete functionality
        const suggestions = ref([]);
        const showSuggestions = ref(false);
        const highlightedIndex = ref(-1);
        const isSearching = ref(false);
        const searchInput = ref(null);
        let searchTimeout = null;

        const activityName = ref('');
        const activityDate = ref(new Date().toISOString().split('T')[0]);
        const activityTime = ref('08:00');
        const routeType = ref('running');
        const pace = ref(5.5);
        const paceVariability = ref(5);
        const avgHeartRate = ref(155);
        const cadence = ref(170);
        const activityGenerated = ref(false);

        // Computed properties
        const calculatedDuration = computed(() => {
            if (routePoints.value.length < 2) return 0;
            // Convert pace (min/km) to seconds and multiply by distance
            return pace.value * 60 * parseFloat(distance.value);
        });

        onMounted(() => {
            // Initialize the map
            initMap();
        });

        onUnmounted(() => {
            // Clean up search timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
        });

        const initMap = () => {
            // Fix Leaflet's default icon path issue
            delete L.Icon.Default.prototype._getIconUrl;
            L.Icon.Default.mergeOptions({
                iconRetinaUrl: '/images/leaflet/marker-icon-2x.png',
                iconUrl: '/images/leaflet/marker-icon.png',
                shadowUrl: '/images/leaflet/marker-shadow.png'
            });

            // Initialize the map
            map.value = L.map('map').setView([16.0544, 108.2022], 13);

            // Get Mapbox access token from environment
            const mapboxToken = process.env.MIX_MAPBOX_ACCESS_TOKEN;

            if (!mapboxToken) {
                console.error('Mapbox access token not found. Please set MIX_MAPBOX_ACCESS_TOKEN in your environment.');
                // Fallback to OpenStreetMap if no Mapbox token
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap'
                }).addTo(map.value);
            } else {
                // Add Mapbox tile layer
                L.tileLayer('https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token=' + mapboxToken, {
                    attribution: '© Mapbox © OpenStreetMap',
                    tileSize: 512,
                    zoomOffset: -1
                }).addTo(map.value);
            }

            // Initialize the route layer
            routeLayer.value = L.featureGroup().addTo(map.value);

            // Add click event handler for the map
            map.value.on('click', handleMapClick);
        };

        const handleMapClick = (e) => {
            if (drawingMode.value === 'point') {
                addRoutePoint(e.latlng);
            }
        };

        const addRoutePoint = (latlng) => {
            // Add a marker at the clicked location
            L.marker(latlng).addTo(routeLayer.value);

            // Add the point to our route points array
            routePoints.value.push([latlng.lat, latlng.lng]);

            // If we have at least 2 points, draw a line
            if (routePoints.value.length >= 2) {
                drawRouteLine();
            }

            // Calculate route metrics
            calculateRouteMetrics();
        };

        const drawRouteLine = () => {
            // Clear existing polyline
            routeLayer.value.eachLayer(layer => {
                if (layer instanceof L.Polyline) {
                    routeLayer.value.removeLayer(layer);
                }
            });

            // Draw a new polyline with all points - using Strava orange color
            L.polyline(routePoints.value, { color: '#FC4C02', weight: 4 }).addTo(routeLayer.value);
        };

        // Rest of the code remains the same
        const calculateRouteMetrics = () => {
            // Calculate distance (a simplified version)
            let totalDistance = 0;
            for (let i = 1; i < routePoints.value.length; i++) {
                const p1 = L.latLng(routePoints.value[i-1][0], routePoints.value[i-1][1]);
                const p2 = L.latLng(routePoints.value[i][0], routePoints.value[i][1]);
                totalDistance += p1.distanceTo(p2);
            }

            // Convert to kilometers and round to 2 decimal places
            distance.value = (totalDistance / 1000).toFixed(2);

            // For demo purposes, generate a random elevation gain
            elevationGain.value = Math.floor(Math.random() * 100) + 50;
        };

        const setDrawingMode = (mode) => {
            drawingMode.value = mode;
        };

        const clearRoute = () => {
            routeLayer.value.clearLayers();
            routePoints.value = [];
            distance.value = 0;
            elevationGain.value = 0;
            activityGenerated.value = false;
            alignmentError.value = '';
        };

        const alignToRoad = async () => {
            if (routePoints.value.length < 2) {
                alignmentError.value = 'Please add at least 2 points to align to road.';
                return;
            }

            // Check for too many waypoints (Mapbox limit is 25 for most profiles)
            if (routePoints.value.length > 25) {
                alignmentError.value = 'Too many points selected. Please use 25 or fewer points for route alignment.';
                return;
            }

            // Check for Mapbox access token
            const mapboxToken = process.env.MIX_MAPBOX_ACCESS_TOKEN;
            if (!mapboxToken) {
                alignmentError.value = 'Mapbox access token not configured. Please contact the administrator.';
                return;
            }

            isAligning.value = true;
            alignmentError.value = '';

            try {
                // Validate coordinates are within reasonable bounds
                const invalidPoints = routePoints.value.filter(point =>
                    Math.abs(point[0]) > 90 || Math.abs(point[1]) > 180
                );

                if (invalidPoints.length > 0) {
                    throw new Error('Some route points have invalid coordinates. Please clear the route and try again.');
                }

                // Create coordinates string for Mapbox Directions API (longitude,latitude format)
                const coordinates = routePoints.value
                    .map(point => `${point[1]},${point[0]}`)
                    .join(';');

                // Determine the appropriate routing profile based on activity type
                let routingProfile;
                switch (routeType.value) {
                    case 'bike':
                        routingProfile = 'cycling';
                        break;
                    case 'running':
                    case 'hike':
                        routingProfile = 'walking';
                        break;
                    default:
                        routingProfile = 'walking'; // Default fallback
                }

                // Use Mapbox Directions API with activity-specific profile
                const response = await fetch(
                    `https://api.mapbox.com/directions/v5/mapbox/${routingProfile}/${coordinates}?geometries=geojson&overview=full&access_token=${mapboxToken}`
                );

                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('Invalid Mapbox access token. Please check your configuration.');
                    } else if (response.status === 422) {
                        // More specific error handling for 422 errors
                        const errorData = await response.json().catch(() => ({}));
                        if (errorData.message && errorData.message.includes('No route found')) {
                            throw new Error(`No ${routeType.value} route found between the selected points. Try placing points closer to roads or paths, or use fewer waypoints.`);
                        } else if (errorData.message && errorData.message.includes('coordinate')) {
                            throw new Error('Some points are in inaccessible areas. Please place points on or near roads/paths suitable for the selected activity type.');
                        } else {
                            throw new Error(`Cannot create ${routeType.value} route with current points. Try: 1) Moving points closer to suitable paths, 2) Using fewer waypoints, or 3) Choosing a different activity type.`);
                        }
                    } else if (response.status === 429) {
                        throw new Error('Too many requests to Mapbox. Please wait a moment and try again.');
                    }
                    throw new Error(`Mapbox API error! status: ${response.status}`);
                }

                const data = await response.json();

                if (!data.routes || data.routes.length === 0) {
                    throw new Error(`No ${routeType.value} route found. Try placing points closer to suitable ${routingProfile} paths or using fewer waypoints.`);
                }

                const route = data.routes[0];
                const geometry = route.geometry;

                if (!geometry || !geometry.coordinates || geometry.coordinates.length === 0) {
                    throw new Error('Invalid route geometry received from Mapbox.');
                }

                // Convert the route coordinates to Leaflet format (latitude, longitude)
                const alignedPoints = geometry.coordinates.map(coord => [coord[1], coord[0]]);

                // Update the route points with the aligned route
                routePoints.value = alignedPoints;

                // Redraw the route
                drawRouteLine();

                // Recalculate metrics with the new aligned route
                calculateRouteMetrics();

                // Clear any existing markers and add new ones at the start and end
                routeLayer.value.eachLayer(layer => {
                    if (layer instanceof L.Marker) {
                        routeLayer.value.removeLayer(layer);
                    }
                });

                // Add markers at start and end of aligned route
                if (alignedPoints.length > 0) {
                    L.marker([alignedPoints[0][0], alignedPoints[0][1]]).addTo(routeLayer.value);
                    L.marker([alignedPoints[alignedPoints.length - 1][0], alignedPoints[alignedPoints.length - 1][1]]).addTo(routeLayer.value);
                }

            } catch (error) {
                console.error('Mapbox routing error:', error);
                alignmentError.value = error.message || 'Failed to align route using Mapbox. Please try again or check your internet connection.';
            } finally {
                isAligning.value = false;
            }
        };

        // Autocomplete search functionality
        const searchSuggestions = async (query) => {
            if (!query || query.length < 2) {
                suggestions.value = [];
                showSuggestions.value = false;
                return;
            }

            const mapboxToken = process.env.MIX_MAPBOX_ACCESS_TOKEN;
            if (!mapboxToken) {
                console.error('Mapbox access token not found for geocoding.');
                return;
            }

            isSearching.value = true;
            searchError.value = '';

            try {
                const response = await fetch(
                    `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=${mapboxToken}&limit=5&types=place,locality,neighborhood,address,poi`
                );

                if (!response.ok) {
                    throw new Error(`Geocoding API error: ${response.status}`);
                }

                const data = await response.json();
                suggestions.value = data.features.map(feature => ({
                    id: feature.id,
                    text: feature.text,
                    place_name: feature.place_name,
                    center: feature.center
                }));

                // Show suggestions if we have any
                showSuggestions.value = suggestions.value.length > 0;
            } catch (error) {
                console.error('Geocoding error:', error);
                searchError.value = 'Failed to fetch location suggestions.';
                suggestions.value = [];
                showSuggestions.value = false;
            } finally {
                isSearching.value = false;
            }
        };

        const onSearchInput = () => {
            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Reset suggestions and highlighted index
            highlightedIndex.value = -1;

            // If query is too short, hide suggestions immediately
            if (!searchQuery.value || searchQuery.value.length < 2) {
                suggestions.value = [];
                showSuggestions.value = false;
                return;
            }

            // Debounce search with 500ms delay
            searchTimeout = setTimeout(() => {
                searchSuggestions(searchQuery.value);
            }, 500);
        };

        const selectSuggestion = (suggestion) => {
            searchQuery.value = suggestion.place_name;
            suggestions.value = [];
            showSuggestions.value = false;
            highlightedIndex.value = -1;

            // Center map on selected location
            const [lng, lat] = suggestion.center;
            map.value.setView([lat, lng], 13);
            searchError.value = '';
        };

        const handleKeydown = (event) => {
            if (!showSuggestions.value || suggestions.value.length === 0) {
                return;
            }

            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    highlightedIndex.value = Math.min(highlightedIndex.value + 1, suggestions.value.length - 1);
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (highlightedIndex.value >= 0) {
                        selectSuggestion(suggestions.value[highlightedIndex.value]);
                    } else if (searchQuery.value) {
                        searchLocation();
                    }
                    break;
                case 'Escape':
                    suggestions.value = [];
                    showSuggestions.value = false;
                    highlightedIndex.value = -1;
                    searchInput.value?.blur();
                    break;
            }
        };

        const onSearchBlur = () => {
            // Delay hiding suggestions to allow click events to fire
            setTimeout(() => {
                showSuggestions.value = false;
                highlightedIndex.value = -1;
            }, 150);
        };

        const searchLocation = async () => {
            if (!searchQuery.value) {
                searchError.value = 'Please enter a location to search.';
                return;
            }

            // If we have suggestions, use the first one
            if (suggestions.value.length > 0) {
                selectSuggestion(suggestions.value[0]);
                return;
            }

            // Fallback to direct geocoding search
            const mapboxToken = process.env.MIX_MAPBOX_ACCESS_TOKEN;
            if (!mapboxToken) {
                searchError.value = 'Mapbox access token not configured. Please contact the administrator.';
                return;
            }

            try {
                const response = await fetch(
                    `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(searchQuery.value)}.json?access_token=${mapboxToken}&limit=1`
                );

                if (!response.ok) {
                    throw new Error(`Geocoding API error: ${response.status}`);
                }

                const data = await response.json();

                if (data.features.length === 0) {
                    searchError.value = 'No results found. Please try a different location.';
                    return;
                }

                const feature = data.features[0];
                const [lng, lat] = feature.center;
                map.value.setView([lat, lng], 13);
                searchError.value = '';
            } catch (error) {
                console.error('Search error:', error);
                searchError.value = 'An error occurred while searching. Please try again.';
            }
        };

        const generateActivity = () => {
            if (routePoints.value.length < 2) {
                alert('Please create a route first by adding points on the map');
                return;
            }

            // Mark the activity as generated
            activityGenerated.value = true;

            // Create a route object
            const routeData = {
                name: activityName.value || `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${activityDate.value}`,
                type: routeType.value,
                points: routePoints.value,
                distance: distance.value,
                elevationGain: elevationGain.value
            };

            // Save the activity parameters for export
            const activityData = {
                route: routeData,
                name: activityName.value || `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${activityDate.value}`,
                date: activityDate.value,
                time: activityTime.value,
                pace: pace.value,
                paceVariability: paceVariability.value,
                avgHeartRate: avgHeartRate.value,
                cadence: cadence.value,
                duration: calculatedDuration.value
            };

            localStorage.setItem('createyourrun_current_activity', JSON.stringify(activityData));
        };

        const formatDuration = (seconds) => {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);

            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        };

        const formatPaceDisplay = (paceValue) => {
            const minutes = Math.floor(paceValue);
            const seconds = Math.floor((paceValue - minutes) * 60);
            return `${minutes}:${seconds.toString().padStart(2, '0')} min/km`;
        };

        const goToExport = () => {
            router.push({ name: 'export-activity' });
        };

        const getAlignmentButtonText = () => {
            switch (routeType.value) {
                case 'bike':
                    return 'Align to Bike Routes';
                case 'running':
                    return 'Align to Running Paths';
                case 'hike':
                    return 'Align to Walking Paths';
                default:
                    return 'Align to Road';
            }
        };

        const getAlignmentTooltip = () => {
            switch (routeType.value) {
                case 'bike':
                    return 'Snap route to bike lanes, cycling paths, and bike-friendly roads using Mapbox cycling profile';
                case 'running':
                    return 'Snap route to pedestrian paths, sidewalks, and running-friendly routes using Mapbox walking profile';
                case 'hike':
                    return 'Snap route to walking paths, trails, and pedestrian-friendly routes using Mapbox walking profile';
                default:
                    return 'Snap route to roads and paths using Mapbox';
            }
        };

        // When activityDate changes, update the activityName if it's empty
        watch(activityDate, (newDate) => {
            if (!activityName.value) {
                activityName.value = `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${newDate}`;
            }
        });

        return {
            routePoints,
            distance,
            elevationGain,
            drawingMode,
            setDrawingMode,
            clearRoute,
            alignToRoad,
            isAligning,
            alignmentError,
            searchQuery,
            searchError,
            searchLocation,
            // Autocomplete functionality
            suggestions,
            showSuggestions,
            highlightedIndex,
            isSearching,
            searchInput,
            onSearchInput,
            selectSuggestion,
            handleKeydown,
            onSearchBlur,
            activityName,
            activityDate,
            activityTime,
            routeType,
            pace,
            paceVariability,
            avgHeartRate,
            cadence,
            activityGenerated,
            calculatedDuration,
            generateActivity,
            formatDuration,
            formatPaceDisplay,
            goToExport,
            getAlignmentButtonText,
            getAlignmentTooltip
        };
    }
}
</script>

<style>
.map-container {
    height: 400px;
    width: 100%;
}

/* Strava style customizations */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 4px;
    background: #E6E6EB;
    border-radius: 2px;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FC4C02;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FC4C02;
    cursor: pointer;
}

/* Autocomplete dropdown styles */
.autocomplete-dropdown {
    position: absolute !important;
    z-index: 10000 !important;
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    margin-top: 0.25rem !important;
    max-height: 15rem !important;
    overflow-y: auto !important;
    width: 100% !important;
}
</style>

