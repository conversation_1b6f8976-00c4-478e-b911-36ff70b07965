<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'tokens'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'tokens' => 'integer',
    ];
    
    /**
     * Get the activities associated with the user.
     */
    public function activities()
    {
        return $this->hasMany(Activity::class);
    }
    
    /**
     * Get the exported activities associated with the user.
     */
    public function exportedActivities()
    {
        return $this->hasMany(ExportedActivity::class);
    }
    
    /**
     * Determine if user has available tokens
     * 
     * @return bool
     */
    public function hasAvailableTokens()
    {
        return $this->tokens > 0;
    }
    
    /**
     * Decrement user tokens
     * 
     * @return bool
     */
    public function useToken()
    {
        if ($this->hasAvailableTokens()) {
            $this->tokens -= 1;
            return $this->save();
        }
        
        return false;
    }
}
